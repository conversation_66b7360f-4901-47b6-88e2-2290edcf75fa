
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Advanced Wide Featured Banner */}
      <div className="relative w-full h-[32rem] bg-cover bg-center overflow-hidden"
           style={{
             backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=600&fit=crop')`
           }}>
        {/* Animated Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/30 via-transparent to-purple-900/30"></div>

        {/* Animated Particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-ping opacity-70"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-300 rounded-full animate-ping delay-1000 opacity-70"></div>
          <div className="absolute bottom-1/4 left-1/2 w-1 h-1 bg-purple-300 rounded-full animate-ping delay-2000 opacity-70"></div>
          <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-300 rounded-full animate-ping delay-3000 opacity-70"></div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-8 left-8 w-2 h-16 bg-gradient-to-b from-blue-400 to-transparent rounded-full animate-pulse"></div>
          <div className="absolute top-12 right-12 w-2 h-20 bg-gradient-to-b from-purple-400 to-transparent rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-16 left-16 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
          <div className="absolute bottom-20 right-20 w-2 h-2 bg-blue-400 rounded-full animate-ping delay-500"></div>
        </div>

        {/* Banner Content */}
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="max-w-7xl mx-auto">
            {/* Featured Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-200">Featured Story</span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-lg">
              Star wars jedi: survivor is nearly here - get hype!
            </h1>

            {/* Author Info - Enhanced */}
            <div className="flex items-center gap-4 mb-6 p-3 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 w-fit">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur opacity-75 animate-pulse"></div>
                <img
                  src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                  alt="Amardeep Singh"
                  className="relative w-12 h-12 rounded-full border-2 border-white/70 z-10"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <span className="text-lg font-medium">Amardeep Singh</span>
                <div className="flex items-center gap-2">
                  <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-green-300">Online now</span>
                </div>
              </div>
            </div>

            {/* Like and Heart Icons - Enhanced */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-3">
                <button className="group w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-blue-500/50">
                  <span className="text-white text-lg group-hover:scale-110 transition-transform">👍</span>
                </button>
                <button className="group w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center hover:from-red-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-red-500/50">
                  <span className="text-white text-lg group-hover:scale-110 transition-transform">❤️</span>
                </button>
              </div>
            </div>

            {/* Liked by section - Enhanced */}
            <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 w-fit">
              <div className="flex -space-x-2">
                <div className="w-7 h-7 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-2 border-white shadow-md"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white shadow-md"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full border-2 border-white shadow-md"></div>
              </div>
              <div>
                <span className="text-sm text-blue-200">Liked by</span>
                <span className="text-sm font-medium ml-1">and 1.5K others</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Category Navigation */}
      <div className="relative bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 py-6 sticky top-0 z-30 shadow-2xl backdrop-blur-sm">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 animate-pulse"></div>

        <div className="relative max-w-6xl mx-auto px-4">
          <div className="flex flex-wrap gap-3 sm:gap-5 justify-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`group relative px-8 py-4 rounded-full font-medium transition-all duration-500 flex items-center gap-3 transform hover:scale-105 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-xl hover:shadow-blue-300/50'
                    : 'bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 border border-white/20'
                }`}
              >
                {/* Glow Effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                <span className="relative text-xl group-hover:animate-bounce">{category.icon}</span>
                <span className="relative text-base">{category.name}</span>

                {/* Active Indicator */}
                {activeTab === category.name && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Advanced Main Content */}
      <div className="bg-gradient-to-br from-slate-50 via-white to-blue-50 min-h-screen">
        {/* Background Patterns */}
        <div className="absolute inset-0 z-0 opacity-5">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-blue-400 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-purple-400 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 py-12">
          {/* Main Content Area */}
          <div>
            {/* Sort By Section - Enhanced */}
            <div className="mb-10 flex justify-end">
              <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-xl shadow-lg border border-blue-100">
                <span className="text-gray-700 font-medium">Sort by</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white/70 backdrop-blur-sm border border-blue-200 rounded-lg px-5 py-2.5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-inner"
                >
                  <option value="Most Read">Most Read</option>
                  <option value="Newest">Newest</option>
                  <option value="Oldest">Oldest</option>
                  <option value="Most Liked">Most Liked</option>
                </select>
              </div>
            </div>

            {/* Advanced Content List */}
            <div className="space-y-10">
            {contentItems.map((item) => (
              <div
                key={item.id}
                className="group bg-white border border-blue-100 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden"
              >
                {/* Decorative Background Elements */}
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-blue-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-indigo-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100"></div>

                {/* Content Container */}
                <div className="relative z-10">
                  {/* Author Header - Enhanced */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className="relative group-hover:animate-pulse">
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full blur opacity-0 group-hover:opacity-75 transition duration-500"></div>
                        <img
                          src={item.author.avatar}
                          alt={item.author.name}
                          className="relative w-14 h-14 rounded-full border-2 border-white shadow-lg"
                        />
                      </div>
                      <div>
                        <span className="font-semibold text-gray-800 block">{item.author.name}</span>
                        <span className="text-blue-500 text-sm">Author</span>
                      </div>
                    </div>
                    <button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-300/50">
                      Follow
                    </button>
                  </div>

                  {/* Content Title - Enhanced */}
                  <h2 className="text-2xl font-bold text-gray-800 mb-5 leading-tight group-hover:text-blue-700 transition-colors duration-300">
                    {item.title}
                  </h2>

                  {/* Category and Date - Enhanced */}
                  <div className="flex items-center gap-4 mb-5">
                    <span className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium border border-blue-100 shadow-sm">
                      {item.category}
                    </span>
                    <span className="bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-4 py-2 rounded-full text-sm border border-gray-200 shadow-sm">
                      {item.date}
                    </span>
                  </div>

                  {/* Tags - Enhanced */}
                  <div className="flex flex-wrap gap-3 mb-5">
                    {item.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="text-blue-600 text-sm hover:text-indigo-700 cursor-pointer hover:underline transition-colors duration-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Excerpt - Enhanced */}
                  <p className="text-gray-600 text-base leading-relaxed mb-6 group-hover:text-gray-700 transition-colors duration-300">
                    {item.excerpt}
                  </p>

                  {/* Content Image - Enhanced */}
                  <div className="rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500 transform group-hover:scale-[1.02]">
                    <img
                      src={item.image}
                      alt="Content preview"
                      className="w-full h-64 object-cover"
                    />
                  </div>
                </div>
              </div>
            ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
