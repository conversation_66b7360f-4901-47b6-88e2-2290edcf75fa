
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop'
    },
    {
      id: 3,
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic', '#dragons', '#adventure', '#storytelling'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities. From magic systems to mythical creatures, discover the elements that make fantasy stories unforgettable.',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop'
    },
    {
      id: 4,
      author: {
        name: 'Emma Williams',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Love in the Digital Age: Modern Romance Stories That Touch Hearts',
      category: 'Romance',
      date: 'Jan 12, 2021',
      tags: ['#romance', '#love', '#digital', '#modern', '#relationships', '#heartwarming'],
      excerpt: 'Explore contemporary romance in our connected world, where love finds a way through social media, dating apps, and virtual connections. These stories prove that true love transcends technology and brings people together in unexpected ways.',
      image: 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=400&h=200&fit=crop'
    },
    {
      id: 5,
      author: {
        name: 'David Rodriguez',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Science of Storytelling: How Narratives Shape Our Reality',
      category: 'Science',
      date: 'Feb 18, 2021',
      tags: ['#science', '#psychology', '#storytelling', '#neuroscience', '#cognition', '#research'],
      excerpt: 'Dive into the fascinating intersection of science and storytelling. Discover how our brains are wired for narrative and how stories literally change our neurochemistry, influencing our perceptions, decisions, and even our identity.',
      image: 'https://images.unsplash.com/photo-1532012197267-da84d127e765?w=400&h=200&fit=crop'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Advanced Wide Featured Banner */}
      <div className="relative w-full h-[32rem] bg-cover bg-center overflow-hidden"
           style={{
             backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=600&fit=crop')`
           }}>
        {/* Animated Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/30 via-transparent to-purple-900/30"></div>

        {/* Animated Particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-ping opacity-70"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-300 rounded-full animate-ping delay-1000 opacity-70"></div>
          <div className="absolute bottom-1/4 left-1/2 w-1 h-1 bg-purple-300 rounded-full animate-ping delay-2000 opacity-70"></div>
          <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-300 rounded-full animate-ping delay-3000 opacity-70"></div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-8 left-8 w-2 h-16 bg-gradient-to-b from-blue-400 to-transparent rounded-full animate-pulse"></div>
          <div className="absolute top-12 right-12 w-2 h-20 bg-gradient-to-b from-purple-400 to-transparent rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-16 left-16 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
          <div className="absolute bottom-20 right-20 w-2 h-2 bg-blue-400 rounded-full animate-ping delay-500"></div>
        </div>

        {/* Banner Content */}
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="max-w-7xl mx-auto">
            {/* Featured Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-200">Featured Story</span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-lg">
              Star wars jedi: survivor is nearly here - get hype!
            </h1>

            {/* Author Info - Enhanced */}
            <div className="flex items-center gap-4 mb-6 p-3 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 w-fit">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur opacity-75 animate-pulse"></div>
                <img
                  src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                  alt="Amardeep Singh"
                  className="relative w-12 h-12 rounded-full border-2 border-white/70 z-10"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <span className="text-lg font-medium">Amardeep Singh</span>
                <div className="flex items-center gap-2">
                  <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                  <span className="text-xs text-green-300">Online now</span>
                </div>
              </div>
            </div>

            {/* Like and Heart Icons - Enhanced */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-3">
                <button className="group w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-blue-500/50">
                  <span className="text-white text-lg group-hover:scale-110 transition-transform">👍</span>
                </button>
                <button className="group w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center hover:from-red-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-red-500/50">
                  <span className="text-white text-lg group-hover:scale-110 transition-transform">❤️</span>
                </button>
              </div>
            </div>

            {/* Liked by section - Enhanced */}
            <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 w-fit">
              <div className="flex -space-x-2">
                <div className="w-7 h-7 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-2 border-white shadow-md"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white shadow-md"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full border-2 border-white shadow-md"></div>
              </div>
              <div>
                <span className="text-sm text-blue-200">Liked by</span>
                <span className="text-sm font-medium ml-1">and 1.5K others</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Category Navigation */}
      <div className="bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 py-6 sticky top-0 z-30 shadow-2xl backdrop-blur-sm">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 animate-pulse"></div>

        <div className="relative max-w-6xl mx-auto px-4">
          <div className="flex flex-wrap gap-3 sm:gap-5 justify-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`group relative px-8 py-4 rounded-full font-medium transition-all duration-500 flex items-center gap-3 transform hover:scale-105 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-xl hover:shadow-blue-300/50'
                    : 'bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 border border-white/20'
                }`}
              >
                {/* Glow Effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm"></div>

                <span className="relative text-xl group-hover:animate-bounce">{category.icon}</span>
                <span className="relative text-base">{category.name}</span>

                {/* Active Indicator */}
                {activeTab === category.name && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Advanced Main Content */}
      <div className="bg-gradient-to-br from-slate-50 via-white to-blue-50 min-h-screen">
        {/* Background Patterns */}
        <div className="absolute inset-0 z-0 opacity-5">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-blue-400 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-purple-400 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 max-w-6xl mx-auto px-4 py-12">
          {/* Main Content Area */}
          <div>
            {/* Sort By Section - Enhanced */}
            <div className="mb-10 flex justify-end">
              <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-xl shadow-lg border border-blue-100">
                <span className="text-gray-700 font-medium">Sort by</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white/70 backdrop-blur-sm border border-blue-200 rounded-lg px-5 py-2.5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-inner"
                >
                  <option value="Most Read">Most Read</option>
                  <option value="Newest">Newest</option>
                  <option value="Oldest">Oldest</option>
                  <option value="Most Liked">Most Liked</option>
                </select>
              </div>
            </div>

            {/* Advanced Content List with Scroller */}
            <div className="h-[80vh] overflow-y-auto pr-4 space-y-10 scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-gray-100">
            {contentItems.map((item) => (
              <div
                key={item.id}
                className="group bg-white/90 backdrop-blur-sm border border-blue-100 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden transform hover:-translate-y-2"
              >
                {/* Advanced Decorative Background Elements */}
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100 animate-pulse"></div>

                {/* Floating Particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-1000">
                  <div className="absolute top-10 right-10 w-1 h-1 bg-blue-400 rounded-full animate-ping"></div>
                  <div className="absolute top-20 left-20 w-1 h-1 bg-purple-400 rounded-full animate-ping delay-500"></div>
                  <div className="absolute bottom-20 right-20 w-1 h-1 bg-cyan-400 rounded-full animate-ping delay-1000"></div>
                </div>

                {/* Content Container */}
                <div className="relative z-10">
                  {/* Author Header - Ultra Enhanced */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-5">
                      <div className="relative group-hover:animate-bounce">
                        <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-700 animate-spin-slow"></div>
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full opacity-75 group-hover:opacity-100 transition duration-500"></div>
                        <img
                          src={item.author.avatar}
                          alt={item.author.name}
                          className="relative w-16 h-16 rounded-full border-3 border-white shadow-2xl z-10"
                        />
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                      </div>
                      <div>
                        <span className="font-bold text-gray-800 block text-lg group-hover:text-blue-700 transition-colors duration-300">{item.author.name}</span>
                        <span className="text-blue-500 text-sm font-medium">Featured Author</span>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span className="text-xs text-green-600 font-medium">Active now</span>
                        </div>
                      </div>
                    </div>
                    <button className="group/btn relative bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-700 text-white px-10 py-4 rounded-full font-semibold transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-blue-500/50 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/btn:opacity-100 transition-opacity duration-500"></div>
                      <span className="relative z-10">Follow</span>
                    </button>
                  </div>

                  {/* Content Title - Ultra Enhanced */}
                  <h2 className="text-3xl font-bold text-gray-800 mb-6 leading-tight group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text transition-all duration-500">
                    {item.title}
                  </h2>

                  {/* Category and Date - Ultra Enhanced */}
                  <div className="flex items-center gap-4 mb-6">
                    <span className="relative bg-gradient-to-r from-blue-50 to-indigo-100 text-blue-700 px-5 py-3 rounded-full text-sm font-semibold border border-blue-200 shadow-lg hover:shadow-blue-300/50 transition-all duration-300 overflow-hidden group/cat">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 opacity-0 group-hover/cat:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10">{item.category}</span>
                    </span>
                    <span className="relative bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-5 py-3 rounded-full text-sm font-medium border border-gray-300 shadow-lg hover:shadow-gray-300/50 transition-all duration-300 overflow-hidden group/date">
                      <div className="absolute inset-0 bg-gradient-to-r from-gray-200/20 to-slate-200/20 opacity-0 group-hover/date:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10">{item.date}</span>
                    </span>
                  </div>

                  {/* Tags - Ultra Enhanced */}
                  <div className="flex flex-wrap gap-3 mb-6">
                    {item.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="relative text-blue-600 text-sm font-medium hover:text-white cursor-pointer transition-all duration-300 px-3 py-1 rounded-full border border-blue-200 hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-500 hover:border-transparent hover:shadow-lg transform hover:scale-105"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Excerpt - Ultra Enhanced */}
                  <p className="text-gray-600 text-lg leading-relaxed mb-8 group-hover:text-gray-700 transition-colors duration-300 font-medium">
                    {item.excerpt}
                  </p>

                  {/* Content Image - Ultra Enhanced */}
                  <div className="relative rounded-3xl overflow-hidden shadow-2xl group-hover:shadow-3xl transition-all duration-700 transform group-hover:scale-[1.03]">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <img
                      src={item.image}
                      alt="Content preview"
                      className="w-full h-72 object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
