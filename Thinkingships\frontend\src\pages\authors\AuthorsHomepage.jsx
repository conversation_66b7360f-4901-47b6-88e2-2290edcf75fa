
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://picsum.photos/800/400?random=10'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://picsum.photos/800/400?random=20'
    },
    {
      id: 3,
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic', '#dragons', '#adventure', '#storytelling'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities. From magic systems to mythical creatures, discover the elements that make fantasy stories unforgettable.',
      image: 'https://picsum.photos/800/400?random=30'
    },
    {
      id: 4,
      author: {
        name: 'Emma Williams',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Love in the Digital Age: Modern Romance Stories That Touch Hearts',
      category: 'Romance',
      date: 'Jan 12, 2021',
      tags: ['#romance', '#love', '#digital', '#modern', '#relationships', '#heartwarming'],
      excerpt: 'Explore contemporary romance in our connected world, where love finds a way through social media, dating apps, and virtual connections. These stories prove that true love transcends technology and brings people together in unexpected ways.',
      image: 'https://picsum.photos/800/400?random=40'
    },
    {
      id: 5,
      author: {
        name: 'David Rodriguez',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Science of Storytelling: How Narratives Shape Our Reality',
      category: 'Science',
      date: 'Feb 18, 2021',
      tags: ['#science', '#psychology', '#storytelling', '#neuroscience', '#cognition', '#research'],
      excerpt: 'Dive into the fascinating intersection of science and storytelling. Discover how our brains are wired for narrative and how stories literally change our neurochemistry, influencing our perceptions, decisions, and even our identity.',
      image: 'https://picsum.photos/800/400?random=50'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Ultra-Attractive Wide Featured Banner */}
      <div className="relative w-full h-96 bg-cover bg-center overflow-hidden"
           style={{
             backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=600&fit=crop')`
           }}>
        {/* Multi-layered Animated Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/60 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/40 via-transparent to-purple-900/40"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900/30 via-transparent to-pink-900/30"></div>

        {/* Enhanced Animated Particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-ping opacity-80"></div>
          <div className="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping delay-1000 opacity-80"></div>
          <div className="absolute bottom-1/4 left-1/2 w-2 h-2 bg-purple-300 rounded-full animate-ping delay-2000 opacity-80"></div>
          <div className="absolute top-2/3 right-1/4 w-1.5 h-1.5 bg-cyan-300 rounded-full animate-ping delay-3000 opacity-80"></div>
          <div className="absolute top-1/2 left-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping delay-4000 opacity-70"></div>
          <div className="absolute bottom-1/3 right-1/2 w-1 h-1 bg-pink-300 rounded-full animate-ping delay-5000 opacity-70"></div>
        </div>

        {/* Enhanced Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-8 left-8 w-3 h-20 bg-gradient-to-b from-blue-400 via-cyan-400 to-transparent rounded-full animate-pulse"></div>
          <div className="absolute top-12 right-12 w-3 h-24 bg-gradient-to-b from-purple-400 via-pink-400 to-transparent rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-16 left-16 w-4 h-4 bg-cyan-400 rounded-full animate-ping shadow-lg shadow-cyan-400/50"></div>
          <div className="absolute bottom-20 right-20 w-3 h-3 bg-blue-400 rounded-full animate-ping delay-500 shadow-lg shadow-blue-400/50"></div>
          <div className="absolute top-1/2 left-12 w-2 h-16 bg-gradient-to-b from-white/60 to-transparent rounded-full animate-pulse delay-2000"></div>
          <div className="absolute top-1/3 right-16 w-2 h-12 bg-gradient-to-b from-purple-300 via-pink-300 to-transparent rounded-full animate-pulse delay-3000"></div>
        </div>

        {/* Floating Orbs */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-400/20 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute top-32 right-32 w-24 h-24 bg-purple-400/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-32 left-40 w-40 h-40 bg-cyan-400/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-pink-400/20 rounded-full blur-2xl animate-pulse delay-3000"></div>
        </div>

        {/* Ultra-Enhanced Banner Content */}
        <div className="absolute bottom-0 left-0 right-0 p-10 text-white">
          <div className="max-w-4xl mx-auto px-8">
            {/* Enhanced Featured Badge */}
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-500/30 to-purple-500/30 backdrop-blur-md border border-white/30 rounded-full px-6 py-3 mb-8 shadow-2xl">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
              <span className="text-base font-semibold text-blue-100">Featured Story</span>
              <div className="w-1 h-1 bg-white/60 rounded-full"></div>
              <span className="text-sm text-white/80">Trending Now</span>
            </div>

            {/* Ultra-Enhanced Title */}
            <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-black mb-6 leading-tight text-white drop-shadow-2xl" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.3)'}}>
              Star wars jedi: survivor is nearly here - get hype!
            </h1>

            {/* Ultra-Enhanced Author Info */}
            <div className="flex items-center gap-4 mb-6 p-4 bg-white/15 backdrop-blur-lg rounded-xl border border-white/30 w-fit shadow-xl hover:bg-white/20 transition-all duration-500">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full blur opacity-75 animate-pulse"></div>
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-90"></div>
                <img
                  src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                  alt="Amardeep Singh"
                  className="relative w-12 h-12 rounded-full border-2 border-white/80 z-10 shadow-lg"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse shadow-lg shadow-green-400/50"></div>
              </div>
              <div>
                <span className="text-lg font-bold">Amardeep Singh</span>
                <div className="flex items-center gap-2 mt-1">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-green-300 font-medium">Online now</span>
                  <div className="w-1 h-1 bg-white/50 rounded-full"></div>
                  <span className="text-xs text-blue-200">Featured Author</span>
                </div>
              </div>
            </div>

            {/* Ultra-Enhanced Interaction Buttons */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-3">
                <button className="group relative w-12 h-12 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 transition-all duration-500 transform hover:scale-110 shadow-xl hover:shadow-blue-500/60">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <span className="text-white text-lg group-hover:scale-110 transition-transform duration-300 relative z-10">👍</span>
                </button>
                <button className="group relative w-12 h-12 bg-gradient-to-r from-red-500 via-pink-500 to-rose-600 rounded-full flex items-center justify-center hover:from-red-600 hover:via-pink-600 hover:to-rose-700 transition-all duration-500 transform hover:scale-110 shadow-xl hover:shadow-red-500/60">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <span className="text-white text-lg group-hover:scale-110 transition-transform duration-300 relative z-10">❤️</span>
                </button>
              </div>
            </div>

            {/* Ultra-Enhanced Social Proof */}
            <div className="flex items-center gap-3 bg-white/15 backdrop-blur-lg rounded-full px-4 py-2 border border-white/30 w-fit shadow-xl hover:bg-white/20 transition-all duration-500">
              <div className="flex -space-x-2">
                <div className="w-7 h-7 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-2 border-white shadow-lg"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white shadow-lg"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full border-2 border-white shadow-lg"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-pink-400 to-rose-500 rounded-full border-2 border-white shadow-lg"></div>
              </div>
              <div className="text-sm">
                <span className="text-blue-200 font-medium">Liked by</span>
                <span className="font-bold ml-1 text-white">and 1.5K others</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Ultra-Attractive Category Navigation */}
      <div className="relative bg-gradient-to-r from-blue-600 via-indigo-700 via-purple-700 to-pink-600 py-8 sticky top-0 z-30 shadow-2xl backdrop-blur-lg">
        {/* Multi-layered Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 to-purple-400/30 animate-pulse"></div>
        <div className="absolute inset-0 bg-gradient-to-l from-pink-400/20 to-cyan-400/20 animate-pulse delay-1000"></div>

        {/* Floating Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-2 left-10 w-2 h-2 bg-white/40 rounded-full animate-ping"></div>
          <div className="absolute top-4 right-20 w-1 h-1 bg-blue-300/60 rounded-full animate-ping delay-1000"></div>
          <div className="absolute bottom-2 left-1/3 w-1.5 h-1.5 bg-purple-300/60 rounded-full animate-ping delay-2000"></div>
          <div className="absolute bottom-4 right-1/4 w-1 h-1 bg-pink-300/60 rounded-full animate-ping delay-3000"></div>
        </div>

        <div className="relative max-w-4xl mx-auto px-8">
          <div className="flex gap-4 sm:gap-6 justify-center items-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`group relative px-10 py-5 rounded-full font-bold transition-all duration-700 flex items-center gap-4 transform hover:scale-110 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-2xl hover:shadow-blue-300/60'
                    : 'bg-white/15 backdrop-blur-lg text-white hover:bg-white/25 border border-white/30'
                }`}
              >
                {/* Enhanced Glow Effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/30 via-purple-400/30 to-pink-400/30 opacity-0 group-hover:opacity-100 transition-opacity duration-700 blur-lg"></div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <span className="relative text-2xl group-hover:animate-bounce group-hover:scale-125 transition-transform duration-300">{category.icon}</span>
                <span className="relative text-lg font-semibold">{category.name}</span>

                {/* Enhanced Active Indicator */}
                {activeTab === category.name && (
                  <>
                    <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 rounded-full animate-pulse shadow-lg shadow-blue-500/50"></div>
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white/60 rounded-full animate-ping"></div>
                  </>
                )}

                {/* Hover Particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-1000">
                  <div className="absolute top-1 right-2 w-0.5 h-0.5 bg-white rounded-full animate-ping"></div>
                  <div className="absolute bottom-1 left-2 w-0.5 h-0.5 bg-blue-300 rounded-full animate-ping delay-500"></div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Ultra-Attractive Main Content */}
      <div className="bg-gradient-to-br from-slate-50 via-blue-50 via-purple-50 to-pink-50 min-h-screen relative overflow-hidden">
        {/* Enhanced Background Patterns */}
        <div className="absolute inset-0 z-0 opacity-10">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-gradient-to-br from-indigo-400 to-purple-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
          <div className="absolute top-10 left-10 w-64 h-64 bg-gradient-to-br from-cyan-400 to-blue-400 rounded-full blur-3xl animate-pulse delay-3000"></div>
          <div className="absolute bottom-10 right-10 w-72 h-72 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full blur-3xl animate-pulse delay-4000"></div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-2 h-2 bg-blue-300/40 rounded-full animate-ping"></div>
          <div className="absolute top-40 right-40 w-1.5 h-1.5 bg-purple-300/40 rounded-full animate-ping delay-1000"></div>
          <div className="absolute bottom-40 left-60 w-2 h-2 bg-pink-300/40 rounded-full animate-ping delay-2000"></div>
          <div className="absolute bottom-60 right-20 w-1 h-1 bg-cyan-300/40 rounded-full animate-ping delay-3000"></div>
          <div className="absolute top-60 left-1/3 w-1.5 h-1.5 bg-indigo-300/40 rounded-full animate-ping delay-4000"></div>
          <div className="absolute bottom-20 left-1/2 w-1 h-1 bg-rose-300/40 rounded-full animate-ping delay-5000"></div>
        </div>

        <div className="relative z-10 w-full px-8 py-12">
          {/* Main Content Area */}
          <div>
            {/* Ultra-Enhanced Sort By Section */}
            <div className="mb-12 flex justify-end">
              <div className="flex items-center gap-4 bg-white/90 backdrop-blur-lg px-8 py-4 rounded-2xl shadow-2xl border border-blue-200/50 hover:shadow-blue-200/30 transition-all duration-500">
                <span className="text-gray-700 font-bold text-lg">Sort by</span>
                <div className="relative">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="bg-gradient-to-r from-white to-blue-50 backdrop-blur-sm border-2 border-blue-300 rounded-xl px-6 py-3 text-gray-700 font-semibold focus:outline-none focus:ring-4 focus:ring-blue-400/50 focus:border-blue-500 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                  >
                    <option value="Most Read">Most Read</option>
                    <option value="Newest">Newest</option>
                    <option value="Oldest">Oldest</option>
                    <option value="Most Liked">Most Liked</option>
                  </select>
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Content List with Scroller */}
            <div className="h-[80vh] overflow-y-auto pr-4 space-y-10 scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-gray-100">
            {contentItems.map((item) => (
              <div
                key={item.id}
                className="group bg-white/90 backdrop-blur-sm border border-blue-100 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden transform hover:-translate-y-2"
              >
                {/* Advanced Decorative Background Elements */}
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100 animate-pulse"></div>

                {/* Floating Particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-1000">
                  <div className="absolute top-10 right-10 w-1 h-1 bg-blue-400 rounded-full animate-ping"></div>
                  <div className="absolute top-20 left-20 w-1 h-1 bg-purple-400 rounded-full animate-ping delay-500"></div>
                  <div className="absolute bottom-20 right-20 w-1 h-1 bg-cyan-400 rounded-full animate-ping delay-1000"></div>
                </div>

                {/* Content Container */}
                <div className="relative z-10">
                  {/* Author Header - Ultra Enhanced */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-5">
                      <div className="relative group-hover:animate-bounce">
                        <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full blur opacity-0 group-hover:opacity-100 transition duration-700 animate-spin-slow"></div>
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full opacity-75 group-hover:opacity-100 transition duration-500"></div>
                        <img
                          src={item.author.avatar}
                          alt={item.author.name}
                          className="relative w-16 h-16 rounded-full border-3 border-white shadow-2xl z-10"
                        />
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                      </div>
                      <div>
                        <span className="font-bold text-gray-800 block text-lg group-hover:text-blue-700 transition-colors duration-300">{item.author.name}</span>
                        <span className="text-blue-500 text-sm font-medium">Featured Author</span>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span className="text-xs text-green-600 font-medium">Active now</span>
                        </div>
                      </div>
                    </div>
                    <button className="group/btn relative bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-700 text-white px-10 py-4 rounded-full font-semibold transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-blue-500/50 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover/btn:opacity-100 transition-opacity duration-500"></div>
                      <span className="relative z-10">Follow</span>
                    </button>
                  </div>

                  {/* Content Title - Ultra Enhanced */}
                  <h2 className="text-3xl font-bold text-gray-800 mb-6 leading-tight group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text transition-all duration-500">
                    {item.title}
                  </h2>

                  {/* Category and Date - Ultra Enhanced */}
                  <div className="flex items-center gap-4 mb-6">
                    <span className="relative bg-gradient-to-r from-blue-50 to-indigo-100 text-blue-700 px-5 py-3 rounded-full text-sm font-semibold border border-blue-200 shadow-lg hover:shadow-blue-300/50 transition-all duration-300 overflow-hidden group/cat">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 opacity-0 group-hover/cat:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10">{item.category}</span>
                    </span>
                    <span className="relative bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-5 py-3 rounded-full text-sm font-medium border border-gray-300 shadow-lg hover:shadow-gray-300/50 transition-all duration-300 overflow-hidden group/date">
                      <div className="absolute inset-0 bg-gradient-to-r from-gray-200/20 to-slate-200/20 opacity-0 group-hover/date:opacity-100 transition-opacity duration-300"></div>
                      <span className="relative z-10">{item.date}</span>
                    </span>
                  </div>

                  {/* Tags - Ultra Enhanced */}
                  <div className="flex flex-wrap gap-3 mb-6">
                    {item.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="relative text-blue-600 text-sm font-medium hover:text-white cursor-pointer transition-all duration-300 px-3 py-1 rounded-full border border-blue-200 hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-500 hover:border-transparent hover:shadow-lg transform hover:scale-105"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Excerpt - Ultra Enhanced */}
                  <p className="text-gray-600 text-lg leading-relaxed mb-8 group-hover:text-gray-700 transition-colors duration-300 font-medium">
                    {item.excerpt}
                  </p>

                  {/* Content Image - Ultra Enhanced */}
                  <div className="relative rounded-3xl overflow-hidden shadow-2xl group-hover:shadow-3xl transition-all duration-700 transform group-hover:scale-[1.03]">
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <img
                      src={item.image}
                      alt="Content preview"
                      className="w-full h-72 object-cover transition-transform duration-700 group-hover:scale-110"
                      onError={(e) => {
                        // First fallback - try a different image
                        if (!e.target.dataset.fallback) {
                          e.target.dataset.fallback = "1";
                          const fallbackImages = {
                            'Romance': 'https://picsum.photos/800/400?random=1',
                            'Mystery': 'https://picsum.photos/800/400?random=2',
                            'Fantasy': 'https://picsum.photos/800/400?random=3',
                            'Science': 'https://picsum.photos/800/400?random=4'
                          };
                          e.target.src = fallbackImages[item.category] || 'https://picsum.photos/800/400?random=5';
                        } else {
                          // Final fallback - colored placeholder
                          e.target.src = `https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=${encodeURIComponent(item.category)}`;
                        }
                      }}
                      loading="lazy"
                    />
                    <div className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-4 group-hover:translate-y-0">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
