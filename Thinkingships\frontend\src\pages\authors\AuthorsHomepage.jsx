
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample author profile data
  const authorProfile = {
    name: '<PERSON>',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
  };

  return (
    <div className="w-full bg-gradient-to-br from-indigo-50 via-white to-cyan-50 min-h-screen relative overflow-hidden">
      {/* Main Content Container */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Featured Post Section */}
        <div className="mb-8">
          <div className="relative rounded-2xl overflow-hidden shadow-2xl hover:shadow-3xl transition-all duration-700 group">
            {/* Background Image - Using festive/celebration theme similar to the image */}
            <div
              className="h-80 sm:h-96 bg-cover bg-center relative"
              style={{
                backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=400&fit=crop')`,
                backgroundPosition: 'center center'
              }}
            >
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>

              {/* Content */}
              <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8 text-white">
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-6 leading-tight">
                  Star wars jedi: survivor is nearly here - get hype!
                </h1>

                {/* Author Info */}
                <div className="flex items-center gap-4 mb-6">
                  <img
                    src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                    alt="Amardeep Singh"
                    className="w-12 h-12 rounded-full border-2 border-white/70"
                  />
                  <span className="text-lg font-medium">Amardeep Singh</span>
                </div>

                {/* Like and Heart Icons */}
                <div className="flex items-center gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-10 h-10 bg-blue-500/80 rounded-full flex items-center justify-center hover:bg-blue-600/80 transition-colors cursor-pointer">
                      <span className="text-white text-lg">👍</span>
                    </div>
                    <div className="w-10 h-10 bg-red-500/80 rounded-full flex items-center justify-center hover:bg-red-600/80 transition-colors cursor-pointer">
                      <span className="text-white text-lg">❤️</span>
                    </div>
                  </div>
                </div>

                {/* Liked by section */}
                <div className="flex items-center gap-2 mt-4">
                  <div className="flex -space-x-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full border-2 border-white"></div>
                    <div className="w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                    <div className="w-6 h-6 bg-purple-500 rounded-full border-2 border-white"></div>
                  </div>
                  <span className="text-sm">Liked by</span>
                  <span className="text-sm font-medium">and 1.5K others</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Category Navigation */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 sm:gap-4 justify-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${
                  activeTab === category.name
                    ? 'bg-blue-500 text-white shadow-lg hover:bg-blue-600'
                    : 'bg-white/80 text-gray-600 hover:bg-white hover:text-blue-500 shadow-md'
                }`}
              >
                <span className="text-lg">{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Sort By Section */}
        <div className="mb-8 flex justify-center">
          <div className="flex items-center gap-2">
            <span className="text-gray-600 font-medium">Sort by</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="Most Read">Most Read</option>
              <option value="Newest">Newest</option>
              <option value="Oldest">Oldest</option>
              <option value="Most Liked">Most Liked</option>
            </select>
          </div>
        </div>

        {/* Author Profile Card */}
        <div className="flex justify-center">
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 w-full max-w-md shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
            {/* Enhanced Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>

            <div className="relative z-10 text-center">
              {/* Author Avatar */}
              <div className="mb-6">
                <img
                  src={authorProfile.avatar}
                  alt={authorProfile.name}
                  className="w-20 h-20 rounded-full mx-auto border-4 border-white shadow-lg"
                />
              </div>

              {/* Author Name */}
              <h3 className="text-xl font-bold text-gray-800 mb-6">{authorProfile.name}</h3>

              {/* Follow Button */}
              <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                Follow
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
