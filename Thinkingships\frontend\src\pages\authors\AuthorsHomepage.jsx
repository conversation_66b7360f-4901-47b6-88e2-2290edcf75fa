
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop'
    },
    {
      id: 3,
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic', '#dragons', '#adventure', '#storytelling'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities. From magic systems to mythical creatures, discover the elements that make fantasy stories unforgettable.',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop'
    },
    {
      id: 4,
      author: {
        name: 'Emma Williams',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Love in the Digital Age: Modern Romance Stories That Touch Hearts',
      category: 'Romance',
      date: 'Jan 12, 2021',
      tags: ['#romance', '#love', '#digital', '#modern', '#relationships', '#heartwarming'],
      excerpt: 'Explore contemporary romance in our connected world, where love finds a way through social media, dating apps, and virtual connections. These stories prove that true love transcends technology and brings people together in unexpected ways.',
      image: 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=400&h=200&fit=crop'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Top Banner */}
      <div className="relative h-64 sm:h-80 bg-cover bg-center mb-0"
           style={{
             backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=400&fit=crop')`
           }}>
        {/* Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent"></div>

        {/* Banner Content */}
        <div className="absolute bottom-0 left-0 right-0 p-6 sm:p-8 text-white">
          <div className="max-w-6xl mx-auto">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 leading-tight">
              Star wars jedi: survivor is nearly here - get hype!
            </h1>

            {/* Author Info */}
            <div className="flex items-center gap-4 mb-4">
              <img
                src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                alt="Amardeep Singh"
                className="w-12 h-12 rounded-full border-2 border-white/70"
              />
              <span className="text-lg font-medium">Amardeep Singh</span>
            </div>

            {/* Like and Heart Icons */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 bg-blue-500/80 rounded-full flex items-center justify-center hover:bg-blue-600/80 transition-colors cursor-pointer">
                  <span className="text-white text-lg">👍</span>
                </div>
                <div className="w-10 h-10 bg-red-500/80 rounded-full flex items-center justify-center hover:bg-red-600/80 transition-colors cursor-pointer">
                  <span className="text-white text-lg">❤️</span>
                </div>
              </div>
            </div>

            {/* Liked by section */}
            <div className="flex items-center gap-2">
              <div className="flex -space-x-2">
                <div className="w-6 h-6 bg-blue-500 rounded-full border-2 border-white"></div>
                <div className="w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                <div className="w-6 h-6 bg-purple-500 rounded-full border-2 border-white"></div>
              </div>
              <span className="text-sm">Liked by</span>
              <span className="text-sm font-medium">and 1.5K others</span>
            </div>
          </div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-gradient-to-r from-blue-400 to-blue-600 py-4">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex flex-wrap gap-2 sm:gap-4 justify-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-lg'
                    : 'bg-blue-500/50 text-white hover:bg-white/20'
                }`}
              >
                <span className="text-lg">{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div className="h-screen overflow-y-auto">
        <div className="max-w-6xl mx-auto px-4 py-8">
          {/* Sort By Section */}
          <div className="mb-8 flex justify-end">
            <div className="flex items-center gap-2">
              <span className="text-gray-600 font-medium">Sort by</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="Most Read">Most Read</option>
                <option value="Newest">Newest</option>
                <option value="Oldest">Oldest</option>
                <option value="Most Liked">Most Liked</option>
              </select>
            </div>
          </div>

          {/* Content List */}
          <div className="space-y-6">
          {contentItems.map((item) => (
            <div key={item.id} className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow">
              {/* Author Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <img
                    src={item.author.avatar}
                    alt={item.author.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <span className="font-medium text-gray-800">{item.author.name}</span>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-full font-medium transition-colors">
                  Follow
                </button>
              </div>

              {/* Content Title */}
              <h2 className="text-xl font-bold text-gray-800 mb-4 leading-tight">
                {item.title}
              </h2>

              {/* Category and Date */}
              <div className="flex items-center gap-4 mb-4">
                <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                  {item.category}
                </span>
                <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                  {item.date}
                </span>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {item.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="text-blue-600 text-sm hover:text-blue-800 cursor-pointer"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              {/* Excerpt */}
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                {item.excerpt}
              </p>

              {/* Content Image */}
              <div className="rounded-lg overflow-hidden">
                <img
                  src={item.image}
                  alt="Content preview"
                  className="w-full h-48 object-cover"
                />
              </div>
            </div>
          ))}
              </div>
            </div>

            {/* Right Ad Space */}
            <div className="w-full lg:w-80">
              <div className="sticky top-4">
                <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <div className="text-gray-500 mb-4">
                    <div className="text-4xl mb-2">📢</div>
                    <h3 className="font-semibold text-lg mb-2">Advertisement Space</h3>
                    <p className="text-sm">Your ad could be here!</p>
                  </div>
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg flex items-center justify-center">
                      <span className="text-gray-600 font-medium">300 x 250</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
