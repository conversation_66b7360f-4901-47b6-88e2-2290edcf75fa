
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop'
    },
    {
      id: 3,
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic', '#dragons', '#adventure', '#storytelling'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities. From magic systems to mythical creatures, discover the elements that make fantasy stories unforgettable.',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop'
    },
    {
      id: 4,
      author: {
        name: 'Emma Williams',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Love in the Digital Age: Modern Romance Stories That Touch Hearts',
      category: 'Romance',
      date: 'Jan 12, 2021',
      tags: ['#romance', '#love', '#digital', '#modern', '#relationships', '#heartwarming'],
      excerpt: 'Explore contemporary romance in our connected world, where love finds a way through social media, dating apps, and virtual connections. These stories prove that true love transcends technology and brings people together in unexpected ways.',
      image: 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=400&h=200&fit=crop'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Featured Banner Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full opacity-20">
            <div className="absolute top-10 left-10 w-32 h-32 bg-blue-400 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute top-20 right-20 w-24 h-24 bg-purple-400 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute bottom-10 left-1/3 w-40 h-40 bg-cyan-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
          </div>
        </div>

        {/* Main Banner Container */}
        <div className="relative">
          {/* Background Image with Enhanced Overlay */}
          <div className="relative h-80 sm:h-96 lg:h-[28rem] bg-cover bg-center"
               style={{
                 backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=400&fit=crop')`
               }}>

            {/* Multi-layered Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/30 via-transparent to-purple-900/30"></div>

            {/* Decorative Elements */}
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-8 left-8 w-2 h-16 bg-gradient-to-b from-blue-400 to-transparent rounded-full"></div>
              <div className="absolute top-12 right-12 w-2 h-20 bg-gradient-to-b from-purple-400 to-transparent rounded-full"></div>
              <div className="absolute bottom-16 left-16 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
              <div className="absolute bottom-20 right-20 w-2 h-2 bg-blue-400 rounded-full animate-ping delay-500"></div>
            </div>

            {/* Enhanced Banner Content */}
            <div className="absolute bottom-0 left-0 right-0 p-8 sm:p-12 text-white">
              <div className="max-w-6xl mx-auto">
                {/* Featured Badge */}
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-blue-200">Featured Story</span>
                </div>

                {/* Enhanced Title */}
                <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 leading-tight bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-2xl">
                  Star wars jedi: survivor is nearly here - get hype!
                </h1>

                {/* Enhanced Author Info */}
                <div className="flex items-center gap-4 mb-6 p-4 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 w-fit">
                  <div className="relative">
                    <img
                      src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                      alt="Amardeep Singh"
                      className="w-14 h-14 rounded-full border-3 border-white/70 shadow-xl"
                    />
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-400 rounded-full border-2 border-white"></div>
                  </div>
                  <div>
                    <span className="text-lg font-semibold block">Amardeep Singh</span>
                    <span className="text-blue-200 text-sm">Featured Author</span>
                  </div>
                </div>

                {/* Enhanced Interaction Section */}
                <div className="flex items-center gap-6 mb-6">
                  {/* Enhanced Like and Heart Icons */}
                  <div className="flex items-center gap-3">
                    <button className="group relative w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl">
                      <span className="text-white text-lg group-hover:scale-110 transition-transform">👍</span>
                      <div className="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    </button>
                    <button className="group relative w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center hover:from-red-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl">
                      <span className="text-white text-lg group-hover:scale-110 transition-transform">❤️</span>
                      <div className="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    </button>
                  </div>

                  {/* Enhanced Liked by section */}
                  <div className="flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                    <div className="flex -space-x-2">
                      <div className="w-7 h-7 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-2 border-white shadow-md"></div>
                      <div className="w-7 h-7 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white shadow-md"></div>
                      <div className="w-7 h-7 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full border-2 border-white shadow-md"></div>
                    </div>
                    <div className="text-sm">
                      <span className="text-blue-200">Liked by</span>
                      <span className="font-semibold ml-1">and 1.5K others</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-gradient-to-r from-blue-400 to-blue-600 py-4">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex flex-wrap gap-2 sm:gap-4 justify-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-lg'
                    : 'bg-blue-500/50 text-white hover:bg-white/20'
                }`}
              >
                <span className="text-lg">{category.icon}</span>
                <span>{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div className="h-screen overflow-y-auto">
        <div className="max-w-6xl mx-auto px-4 py-8">
          {/* Sort By Section */}
          <div className="mb-8 flex justify-end">
            <div className="flex items-center gap-2">
              <span className="text-gray-600 font-medium">Sort by</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="Most Read">Most Read</option>
                <option value="Newest">Newest</option>
                <option value="Oldest">Oldest</option>
                <option value="Most Liked">Most Liked</option>
              </select>
            </div>
          </div>

          {/* Content List */}
          <div className="space-y-6">
          {contentItems.map((item) => (
            <div key={item.id} className="bg-white border border-gray-200 rounded-2xl p-6 shadow-sm hover:shadow-md transition-shadow">
              {/* Author Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <img
                    src={item.author.avatar}
                    alt={item.author.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <span className="font-medium text-gray-800">{item.author.name}</span>
                </div>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-full font-medium transition-colors">
                  Follow
                </button>
              </div>

              {/* Content Title */}
              <h2 className="text-xl font-bold text-gray-800 mb-4 leading-tight">
                {item.title}
              </h2>

              {/* Category and Date */}
              <div className="flex items-center gap-4 mb-4">
                <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                  {item.category}
                </span>
                <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
                  {item.date}
                </span>
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-4">
                {item.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="text-blue-600 text-sm hover:text-blue-800 cursor-pointer"
                  >
                    {tag}
                  </span>
                ))}
              </div>

              {/* Excerpt */}
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                {item.excerpt}
              </p>

              {/* Content Image */}
              <div className="rounded-lg overflow-hidden">
                <img
                  src={item.image}
                  alt="Content preview"
                  className="w-full h-48 object-cover"
                />
              </div>
            </div>
          ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
