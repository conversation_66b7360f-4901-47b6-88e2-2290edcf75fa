
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://picsum.photos/800/400?random=10'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://picsum.photos/800/400?random=20'
    },
    {
      id: 3,
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic', '#dragons', '#adventure', '#storytelling'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities. From magic systems to mythical creatures, discover the elements that make fantasy stories unforgettable.',
      image: 'https://picsum.photos/800/400?random=30'
    },
    {
      id: 4,
      author: {
        name: 'Emma Williams',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Love in the Digital Age: Modern Romance Stories That Touch Hearts',
      category: 'Romance',
      date: 'Jan 12, 2021',
      tags: ['#romance', '#love', '#digital', '#modern', '#relationships', '#heartwarming'],
      excerpt: 'Explore contemporary romance in our connected world, where love finds a way through social media, dating apps, and virtual connections. These stories prove that true love transcends technology and brings people together in unexpected ways.',
      image: 'https://picsum.photos/800/400?random=40'
    },
    {
      id: 5,
      author: {
        name: 'David Rodriguez',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Science of Storytelling: How Narratives Shape Our Reality',
      category: 'Science',
      date: 'Feb 18, 2021',
      tags: ['#science', '#psychology', '#storytelling', '#neuroscience', '#cognition', '#research'],
      excerpt: 'Dive into the fascinating intersection of science and storytelling. Discover how our brains are wired for narrative and how stories literally change our neurochemistry, influencing our perceptions, decisions, and even our identity.',
      image: 'https://picsum.photos/800/400?random=50'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Featured Banner */}
      <div className="relative w-full h-80 sm:h-96 lg:h-[500px] bg-cover bg-center overflow-hidden"
           style={{
             backgroundImage: `url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=600&fit=crop')`
           }}>
        {/* Enhanced Overlay for Better Text Visibility */}
        <div className="absolute inset-0 bg-black/70"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/20"></div>

        {/* Enhanced Animated Particles */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-ping opacity-80"></div>
          <div className="absolute top-1/3 right-1/3 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping delay-1000 opacity-80"></div>
          <div className="absolute bottom-1/4 left-1/2 w-2 h-2 bg-purple-300 rounded-full animate-ping delay-2000 opacity-80"></div>
          <div className="absolute top-2/3 right-1/4 w-1.5 h-1.5 bg-cyan-300 rounded-full animate-ping delay-3000 opacity-80"></div>
          <div className="absolute top-1/2 left-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping delay-4000 opacity-70"></div>
          <div className="absolute bottom-1/3 right-1/2 w-1 h-1 bg-pink-300 rounded-full animate-ping delay-5000 opacity-70"></div>
        </div>

        {/* Enhanced Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-8 left-8 w-3 h-20 bg-gradient-to-b from-blue-400 via-cyan-400 to-transparent rounded-full animate-pulse"></div>
          <div className="absolute top-12 right-12 w-3 h-24 bg-gradient-to-b from-purple-400 via-pink-400 to-transparent rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-16 left-16 w-4 h-4 bg-cyan-400 rounded-full animate-ping shadow-lg shadow-cyan-400/50"></div>
          <div className="absolute bottom-20 right-20 w-3 h-3 bg-blue-400 rounded-full animate-ping delay-500 shadow-lg shadow-blue-400/50"></div>
          <div className="absolute top-1/2 left-12 w-2 h-16 bg-gradient-to-b from-white/60 to-transparent rounded-full animate-pulse delay-2000"></div>
          <div className="absolute top-1/3 right-16 w-2 h-12 bg-gradient-to-b from-purple-300 via-pink-300 to-transparent rounded-full animate-pulse delay-3000"></div>
        </div>

        {/* Floating Orbs */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-400/20 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute top-32 right-32 w-24 h-24 bg-purple-400/20 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-32 left-40 w-40 h-40 bg-cyan-400/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-pink-400/20 rounded-full blur-2xl animate-pulse delay-3000"></div>
        </div>

        {/* Banner Content */}
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="max-w-6xl mx-auto px-6">
            {/* Featured Badge */}
            <div className="inline-flex items-center gap-2 bg-black/60 backdrop-blur-sm border border-white/40 rounded-full px-5 py-2.5 mb-6">
              <div className="w-2.5 h-2.5 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
              <span className="text-base font-semibold text-white drop-shadow-lg" style={{textShadow: '3px 3px 6px rgba(0,0,0,0.9)'}}>Featured Story</span>
              <div className="w-1 h-1 bg-white/60 rounded-full mx-1"></div>
              <span className="text-sm text-blue-200 font-medium">Trending Now</span>
            </div>

            {/* Title */}
            <div className="mb-6">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight text-white mb-1"
                  style={{
                    textShadow: '3px 3px 6px rgba(0,0,0,1), 0 0 20px rgba(0,0,0,0.8), 2px 2px 4px rgba(0,0,0,1)',
                    WebkitTextStroke: '0.5px rgba(0,0,0,0.2)'
                  }}>
                Star wars jedi: survivor is
              </h1>
              <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold leading-tight text-white underline decoration-2 underline-offset-4"
                  style={{
                    textShadow: '3px 3px 6px rgba(0,0,0,1), 0 0 20px rgba(0,0,0,0.8), 2px 2px 4px rgba(0,0,0,1)',
                    WebkitTextStroke: '0.5px rgba(0,0,0,0.2)'
                  }}>
                nearly here - get hype!
              </h2>
            </div>

            {/* Author Info */}
            <div className="flex items-center gap-4 mb-6 p-4 bg-black/50 backdrop-blur-sm rounded-xl border border-white/40 w-fit shadow-2xl">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full blur opacity-75 animate-pulse"></div>
                <img
                  src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                  alt="Amardeep Singh"
                  className="relative w-12 h-12 rounded-full border-3 border-white shadow-lg z-10"
                />
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse shadow-lg shadow-green-400/50"></div>
              </div>
              <div>
                <span className="text-lg font-bold text-white drop-shadow-lg" style={{textShadow: '3px 3px 6px rgba(0,0,0,0.9)'}}>Amardeep Singh</span>
                <div className="flex items-center gap-2 mt-1">
                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm text-green-300 font-medium drop-shadow-lg" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.8)'}}>Online now</span>
                  <div className="w-1 h-1 bg-white/50 rounded-full"></div>
                  <span className="text-xs text-blue-200">Featured Author</span>
                </div>
              </div>
            </div>

            {/* Interaction Buttons */}
            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-3">
                <button className="group relative w-12 h-12 bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 transition-all duration-500 transform hover:scale-110 shadow-xl hover:shadow-blue-500/60">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <span className="text-white text-lg group-hover:scale-110 transition-transform duration-300 relative z-10">👍</span>
                </button>
                <button className="group relative w-12 h-12 bg-gradient-to-r from-red-500 via-pink-500 to-rose-600 rounded-full flex items-center justify-center hover:from-red-600 hover:via-pink-600 hover:to-rose-700 transition-all duration-500 transform hover:scale-110 shadow-xl hover:shadow-red-500/60">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <span className="text-white text-lg group-hover:scale-110 transition-transform duration-300 relative z-10">❤️</span>
                </button>
                <button className="group relative w-12 h-12 bg-gradient-to-r from-gray-600 via-gray-700 to-gray-800 rounded-full flex items-center justify-center hover:from-gray-700 hover:via-gray-800 hover:to-gray-900 transition-all duration-500 transform hover:scale-110 shadow-xl hover:shadow-gray-500/60">
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <span className="text-white text-lg group-hover:scale-110 transition-transform duration-300 relative z-10">🔗</span>
                </button>
              </div>
            </div>

            {/* Social Proof */}
            <div className="flex items-center gap-3 bg-black/50 backdrop-blur-sm rounded-full px-4 py-2 border border-white/40 w-fit shadow-xl">
              <div className="flex -space-x-2">
                <div className="w-7 h-7 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-2 border-white shadow-lg"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white shadow-lg"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full border-2 border-white shadow-lg"></div>
                <div className="w-7 h-7 bg-gradient-to-r from-pink-400 to-rose-500 rounded-full border-2 border-white shadow-lg"></div>
              </div>
              <div className="text-sm">
                <span className="text-blue-200 font-medium drop-shadow-lg" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.8)'}}>Liked by</span>
                <span className="font-bold ml-1 text-white drop-shadow-lg" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.8)'}}>and 1.5K others</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-gradient-to-r from-blue-400 to-blue-600 py-4 sticky top-0 z-30 shadow-lg">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex gap-2 sm:gap-4 justify-center items-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`px-4 py-2 rounded-full font-medium transition-all duration-300 flex items-center gap-2 transform hover:scale-105 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-md'
                    : 'bg-blue-500/50 text-white hover:bg-white/20'
                }`}
              >
                <span className="text-base">{category.icon}</span>
                <span className="text-sm font-medium">{category.name}</span>

                {/* Active Indicator */}
                {activeTab === category.name && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Ultra-Attractive Main Content */}
      <div className="bg-gradient-to-br from-slate-50 via-blue-50 via-purple-50 to-pink-50 min-h-screen relative overflow-hidden">
        {/* Enhanced Background Patterns */}
        <div className="absolute inset-0 z-0 opacity-10">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-gradient-to-br from-indigo-400 to-purple-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
          <div className="absolute top-10 left-10 w-64 h-64 bg-gradient-to-br from-cyan-400 to-blue-400 rounded-full blur-3xl animate-pulse delay-3000"></div>
          <div className="absolute bottom-10 right-10 w-72 h-72 bg-gradient-to-br from-pink-400 to-rose-400 rounded-full blur-3xl animate-pulse delay-4000"></div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-20 w-2 h-2 bg-blue-300/40 rounded-full animate-ping"></div>
          <div className="absolute top-40 right-40 w-1.5 h-1.5 bg-purple-300/40 rounded-full animate-ping delay-1000"></div>
          <div className="absolute bottom-40 left-60 w-2 h-2 bg-pink-300/40 rounded-full animate-ping delay-2000"></div>
          <div className="absolute bottom-60 right-20 w-1 h-1 bg-cyan-300/40 rounded-full animate-ping delay-3000"></div>
          <div className="absolute top-60 left-1/3 w-1.5 h-1.5 bg-indigo-300/40 rounded-full animate-ping delay-4000"></div>
          <div className="absolute bottom-20 left-1/2 w-1 h-1 bg-rose-300/40 rounded-full animate-ping delay-5000"></div>
        </div>

        <div className="relative z-10 flex gap-6 px-4 py-8 max-w-7xl mx-auto">
          {/* Main Content Area */}
          <div className="flex-1 max-w-4xl">
          {/* Main Content Area */}
          <div>
            {/* Sort By Section */}
            <div className="mb-8 flex justify-end">
              <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-4 py-2 rounded-lg shadow-lg border border-blue-100">
                <span className="text-gray-600 font-medium">Sort by</span>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white border border-gray-300 rounded-lg px-3 py-1.5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="Most Read">Most Read</option>
                  <option value="Newest">Newest</option>
                  <option value="Oldest">Oldest</option>
                  <option value="Most Liked">Most Liked</option>
                </select>
              </div>
            </div>

            {/* Advanced Content List with Scroller */}
            <div className="h-[70vh] overflow-y-auto pr-2 space-y-6 scrollbar-thin scrollbar-thumb-blue-300 scrollbar-track-gray-100">
            {contentItems.map((item) => (
              <div
                key={item.id}
                className="group bg-white/90 backdrop-blur-sm border border-blue-100 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden transform hover:-translate-y-1"
              >
                {/* Advanced Decorative Background Elements */}
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>
                <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100 animate-pulse"></div>

                {/* Floating Particles */}
                <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-1000">
                  <div className="absolute top-10 right-10 w-1 h-1 bg-blue-400 rounded-full animate-ping"></div>
                  <div className="absolute top-20 left-20 w-1 h-1 bg-purple-400 rounded-full animate-ping delay-500"></div>
                  <div className="absolute bottom-20 right-20 w-1 h-1 bg-cyan-400 rounded-full animate-ping delay-1000"></div>
                </div>

                {/* Content Container */}
                <div className="relative z-10">
                  {/* Author Header - Enhanced */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur opacity-0 group-hover:opacity-75 transition duration-500"></div>
                        <img
                          src={item.author.avatar}
                          alt={item.author.name}
                          className="relative w-12 h-12 rounded-full border-2 border-white shadow-lg z-10"
                        />
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                      </div>
                      <div>
                        <span className="font-semibold text-gray-800 block group-hover:text-blue-700 transition-colors duration-300">{item.author.name}</span>
                        <span className="text-blue-500 text-sm">Author</span>
                      </div>
                    </div>
                    <button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-2 rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/50">
                      Follow
                    </button>
                  </div>

                  {/* Content Title - Enhanced */}
                  <h2 className="text-xl font-bold text-gray-800 mb-4 leading-tight group-hover:text-blue-700 transition-colors duration-300">
                    {item.title}
                  </h2>

                  {/* Category and Date - Enhanced */}
                  <div className="flex items-center gap-4 mb-4">
                    <span className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium border border-blue-100 shadow-sm">
                      {item.category}
                    </span>
                    <span className="bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-3 py-1 rounded-full text-sm border border-gray-200 shadow-sm">
                      {item.date}
                    </span>
                  </div>

                  {/* Tags - Enhanced */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="text-blue-600 text-sm hover:text-blue-800 cursor-pointer transition-colors duration-300"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Excerpt - Enhanced */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-4 group-hover:text-gray-700 transition-colors duration-300">
                    {item.excerpt}
                  </p>

                  {/* Content Image - Enhanced */}
                  <div className="relative rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500 transform group-hover:scale-[1.02]">
                    <img
                      src={item.image}
                      alt="Content preview"
                      className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
                      onError={(e) => {
                        // First fallback - try a different image
                        if (!e.target.dataset.fallback) {
                          e.target.dataset.fallback = "1";
                          const fallbackImages = {
                            'Romance': 'https://picsum.photos/800/400?random=1',
                            'Mystery': 'https://picsum.photos/800/400?random=2',
                            'Fantasy': 'https://picsum.photos/800/400?random=3',
                            'Science': 'https://picsum.photos/800/400?random=4'
                          };
                          e.target.src = fallbackImages[item.category] || 'https://picsum.photos/800/400?random=5';
                        } else {
                          // Final fallback - colored placeholder
                          e.target.src = `https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=${encodeURIComponent(item.category)}`;
                        }
                      }}
                      loading="lazy"
                    />
                  </div>
                </div>
              </div>
            ))}
            </div>
          </div>
          </div>

          {/* Right Sidebar - Scrollable Ad Space */}
          <div className="w-80 bg-gray-50 rounded-lg shadow-lg border border-gray-300 sticky top-24 h-fit">
            {/* Ad Space Header */}
            <div className="p-4 border-b border-gray-300">
              <h3 className="text-base font-medium text-gray-600">Ad space</h3>
            </div>

            {/* Scrollable Ad Container */}
            <div className="h-[600px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200 p-4">
              <div className="space-y-4">
                {/* Empty space for future ads - this will scroll when ads are added */}
                <div className="h-full flex items-center justify-center text-gray-400">
                  <p className="text-sm">Ads will appear here</p>
                </div>

                {/* Future ads will be added here and will automatically scroll */}
                {/* Example ad structure:
                <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                  <div className="w-full h-32 bg-gray-200 rounded mb-3"></div>
                  <h4 className="font-semibold text-gray-800 mb-2">Ad Title</h4>
                  <p className="text-sm text-gray-600 mb-3">Ad description...</p>
                  <button className="w-full bg-blue-500 text-white py-2 px-4 rounded font-medium">
                    Click Here
                  </button>
                </div>
                */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
