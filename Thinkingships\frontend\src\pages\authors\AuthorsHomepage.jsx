
import { useState } from 'react';

function AuthorsHomepage() {
  const [activeTab, setActiveTab] = useState('Stories');
  const [sortBy, setSortBy] = useState('Most Read');

  // Sample data for categories
  const categories = [
    { name: 'Stories', icon: '📚', active: true },
    { name: 'Poems', icon: '📝', active: false },
    { name: 'Blogs', icon: '📰', active: false },
    { name: 'e-books', icon: '📖', active: false }
  ];

  // Sample content data
  const contentItems = [
    {
      id: 1,
      author: {
        name: '<PERSON>',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Can We Survive The New Campaign Of Age Of Darkness Final Stand',
      category: 'Romance',
      date: 'Oct 27, 2020',
      tags: ['#isseeshat', '#mysteries', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure', '#adventure'],
      excerpt: 'Lorem ipsum dolor sit amet consectetur. Eros commodo accumsan ullamcorper imperdiet sed ullamcorper dolor. Eget duis fermentum vestibulum ac diam tristique ut sed et. Scelerisque amet risus aliquet felis. Gravida ultrices tempus varius blandit suscipit augue netus odio ornare cursus nibh vestibulum.',
      image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=200&fit=crop'
    },
    {
      id: 2,
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'The Mystery Behind Ancient Civilizations and Their Lost Technologies',
      category: 'Mystery',
      date: 'Nov 15, 2020',
      tags: ['#mystery', '#ancient', '#technology', '#history', '#civilization', '#secrets'],
      excerpt: 'Discover the fascinating world of ancient civilizations and their mysterious technologies that continue to baffle modern scientists. From the pyramids of Egypt to the stone circles of England, these monuments hold secrets that challenge our understanding of human history.',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=200&fit=crop'
    },
    {
      id: 3,
      author: {
        name: 'Michael Chen',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Fantasy Realms: Building Worlds That Captivate Readers',
      category: 'Fantasy',
      date: 'Dec 03, 2020',
      tags: ['#fantasy', '#worldbuilding', '#magic', '#dragons', '#adventure', '#storytelling'],
      excerpt: 'Learn the art of creating immersive fantasy worlds that transport readers to magical realms filled with wonder, danger, and endless possibilities. From magic systems to mythical creatures, discover the elements that make fantasy stories unforgettable.',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop'
    },
    {
      id: 4,
      author: {
        name: 'Emma Williams',
        avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>'
      },
      title: 'Love in the Digital Age: Modern Romance Stories That Touch Hearts',
      category: 'Romance',
      date: 'Jan 12, 2021',
      tags: ['#romance', '#love', '#digital', '#modern', '#relationships', '#heartwarming'],
      excerpt: 'Explore contemporary romance in our connected world, where love finds a way through social media, dating apps, and virtual connections. These stories prove that true love transcends technology and brings people together in unexpected ways.',
      image: 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=400&h=200&fit=crop'
    }
  ];

  return (
    <div className="w-full bg-white min-h-screen">
      {/* Full Screen Featured Banner Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 min-h-screen">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full opacity-20">
            <div className="absolute top-10 left-10 w-32 h-32 bg-blue-400 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute top-20 right-20 w-24 h-24 bg-purple-400 rounded-full blur-2xl animate-pulse delay-1000"></div>
            <div className="absolute bottom-10 left-1/3 w-40 h-40 bg-cyan-400 rounded-full blur-3xl animate-pulse delay-2000"></div>
            <div className="absolute top-1/2 left-1/2 w-60 h-60 bg-indigo-400 rounded-full blur-3xl animate-pulse delay-3000 opacity-30"></div>
          </div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-ping delay-1000"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-300 rounded-full animate-ping delay-2000"></div>
          <div className="absolute bottom-1/4 left-1/2 w-1 h-1 bg-purple-300 rounded-full animate-ping delay-3000"></div>
          <div className="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-300 rounded-full animate-ping delay-4000"></div>
        </div>

        {/* Main Banner Container */}
        <div className="relative min-h-screen flex items-center">
          {/* Background Image with Enhanced Overlay */}
          <div className="absolute inset-0 bg-cover bg-center"
               style={{
                 backgroundImage: `url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1920&h=1080&fit=crop')`
               }}>

            {/* Multi-layered Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/30 via-transparent to-purple-900/30"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/70"></div>

            {/* Decorative Elements */}
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-8 left-8 w-2 h-16 bg-gradient-to-b from-blue-400 to-transparent rounded-full animate-pulse"></div>
              <div className="absolute top-12 right-12 w-2 h-20 bg-gradient-to-b from-purple-400 to-transparent rounded-full animate-pulse delay-1000"></div>
              <div className="absolute bottom-16 left-16 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>
              <div className="absolute bottom-20 right-20 w-2 h-2 bg-blue-400 rounded-full animate-ping delay-500"></div>
              <div className="absolute top-1/2 left-8 w-1 h-12 bg-gradient-to-b from-white/50 to-transparent rounded-full animate-pulse delay-2000"></div>
              <div className="absolute top-1/3 right-8 w-1 h-8 bg-gradient-to-b from-purple-300 to-transparent rounded-full animate-pulse delay-3000"></div>
            </div>
          </div>

          {/* Enhanced Banner Content */}
          <div className="relative z-10 w-full p-8 sm:p-12 lg:p-16 text-white">
            <div className="max-w-7xl mx-auto">
              {/* Featured Badge */}
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8 shadow-2xl">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-base font-medium text-blue-200">Featured Story</span>
                <div className="w-1 h-1 bg-white/50 rounded-full"></div>
                <span className="text-sm text-white/70">Trending Now</span>
              </div>

              {/* Enhanced Title */}
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold mb-8 leading-tight bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-2xl animate-fade-in">
                Star wars jedi: survivor is nearly here - get hype!
              </h1>

              {/* Enhanced Author Info */}
              <div className="flex items-center gap-6 mb-8 p-6 bg-white/10 backdrop-blur-md rounded-3xl border border-white/20 w-fit shadow-2xl hover:bg-white/15 transition-all duration-500 group">
                <div className="relative">
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-500"></div>
                  <img
                    src="https://img.daisyui.com/images/profile/demo/<EMAIL>"
                    alt="Amardeep Singh"
                    className="relative w-16 h-16 rounded-full border-3 border-white/70 shadow-xl"
                  />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-3 border-white animate-pulse"></div>
                </div>
                <div>
                  <span className="text-xl font-semibold block">Amardeep Singh</span>
                  <span className="text-blue-200 text-base">Featured Author</span>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                    <span className="text-xs text-green-300">Online now</span>
                  </div>
                </div>
              </div>

              {/* Enhanced Interaction Section */}
              <div className="flex items-center gap-8 mb-8">
                {/* Enhanced Like and Heart Icons */}
                <div className="flex items-center gap-4">
                  <button className="group relative w-14 h-14 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-blue-500/50">
                    <span className="text-white text-xl group-hover:scale-110 transition-transform">👍</span>
                    <div className="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full blur opacity-0 group-hover:opacity-75 transition duration-500"></div>
                  </button>
                  <button className="group relative w-14 h-14 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center hover:from-red-600 hover:to-pink-600 transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-red-500/50">
                    <span className="text-white text-xl group-hover:scale-110 transition-transform">❤️</span>
                    <div className="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-red-400 to-pink-600 rounded-full blur opacity-0 group-hover:opacity-75 transition duration-500"></div>
                  </button>
                </div>

                {/* Enhanced Liked by section */}
                <div className="flex items-center gap-4 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20 shadow-xl hover:bg-white/15 transition-all duration-300">
                  <div className="flex -space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full border-3 border-white shadow-lg"></div>
                    <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-3 border-white shadow-lg"></div>
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full border-3 border-white shadow-lg"></div>
                  </div>
                  <div className="text-base">
                    <span className="text-blue-200">Liked by</span>
                    <span className="font-semibold ml-1">and 1.5K others</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Navigation with Glass Morphism */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 py-6 sticky top-0 z-30 shadow-xl">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-wrap gap-3 sm:gap-5 justify-center">
            {categories.map((category, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(category.name)}
                className={`px-8 py-4 rounded-full font-medium transition-all duration-500 flex items-center gap-3 transform hover:scale-105 ${
                  activeTab === category.name
                    ? 'bg-white text-blue-600 shadow-lg hover:shadow-blue-300/50'
                    : 'bg-blue-500/40 backdrop-blur-sm text-white hover:bg-white/30 border border-white/20'
                }`}
              >
                <span className="text-xl">{category.icon}</span>
                <span className="text-base">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content with Ad Space - Enhanced UI */}
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content Area */}
            <div className="flex-1">
              {/* Sort By Section - Enhanced */}
              <div className="mb-10 flex justify-end">
                <div className="flex items-center gap-3 bg-white/80 backdrop-blur-sm px-6 py-3 rounded-xl shadow-lg border border-blue-100">
                  <span className="text-gray-700 font-medium">Sort by</span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="bg-white/70 backdrop-blur-sm border border-blue-200 rounded-lg px-5 py-2.5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-inner"
                  >
                    <option value="Most Read">Most Read</option>
                    <option value="Newest">Newest</option>
                    <option value="Oldest">Oldest</option>
                    <option value="Most Liked">Most Liked</option>
                  </select>
                </div>
              </div>

              {/* Content List - Enhanced UI */}
              <div className="space-y-10">
              {contentItems.map((item) => (
                <div
                  key={item.id}
                  className="group bg-white border border-blue-100 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden"
                >
                  {/* Decorative Background Elements */}
                  <div className="absolute -right-20 -top-20 w-40 h-40 bg-blue-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                  <div className="absolute -left-20 -bottom-20 w-40 h-40 bg-indigo-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100"></div>

                  {/* Content Container */}
                  <div className="relative z-10">
                    {/* Author Header - Enhanced */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className="relative group-hover:animate-pulse">
                          <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full blur opacity-0 group-hover:opacity-75 transition duration-500"></div>
                          <img
                            src={item.author.avatar}
                            alt={item.author.name}
                            className="relative w-14 h-14 rounded-full border-2 border-white shadow-lg"
                          />
                        </div>
                        <div>
                          <span className="font-semibold text-gray-800 block">{item.author.name}</span>
                          <span className="text-blue-500 text-sm">Author</span>
                        </div>
                      </div>
                      <button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-300/50">
                        Follow
                      </button>
                    </div>

                    {/* Content Title - Enhanced */}
                    <h2 className="text-2xl font-bold text-gray-800 mb-5 leading-tight group-hover:text-blue-700 transition-colors duration-300">
                      {item.title}
                    </h2>

                    {/* Category and Date - Enhanced */}
                    <div className="flex items-center gap-4 mb-5">
                      <span className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-4 py-2 rounded-full text-sm font-medium border border-blue-100 shadow-sm">
                        {item.category}
                      </span>
                      <span className="bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-4 py-2 rounded-full text-sm border border-gray-200 shadow-sm">
                        {item.date}
                      </span>
                    </div>

                    {/* Tags - Enhanced */}
                    <div className="flex flex-wrap gap-3 mb-5">
                      {item.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="text-blue-600 text-sm hover:text-indigo-700 cursor-pointer hover:underline transition-colors duration-300"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>

                    {/* Excerpt - Enhanced */}
                    <p className="text-gray-600 text-base leading-relaxed mb-6 group-hover:text-gray-700 transition-colors duration-300">
                      {item.excerpt}
                    </p>

                    {/* Content Image - Enhanced */}
                    <div className="rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-shadow duration-500 transform group-hover:scale-[1.02] transition-transform">
                      <img
                        src={item.image}
                        alt="Content preview"
                        className="w-full h-64 object-cover"
                      />
                    </div>
                  </div>
                </div>
              ))}
              </div>
            </div>

            {/* Right Ad Space */}
            <div className="w-full lg:w-80 mt-10 lg:mt-0">
              <div className="sticky top-28">
                <div className="bg-white border border-blue-100 rounded-3xl p-6 shadow-xl">
                  <div className="text-center mb-4">
                    <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full mb-3 shadow-lg">
                      <span className="text-white text-xl">📢</span>
                    </div>
                    <h3 className="font-bold text-xl bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">Advertisement</h3>
                    <p className="text-gray-500 text-sm">Sponsored content</p>
                  </div>

                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 text-center mb-6 shadow-inner">
                    <div className="h-40 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center mb-4">
                      <span className="text-blue-600 font-medium">300 x 250</span>
                    </div>
                    <p className="text-blue-700 text-sm font-medium">Your ad could be here</p>
                  </div>

                  <div className="flex justify-center">
                    <button className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-full text-sm font-medium shadow-lg hover:shadow-blue-300/50 transition-all duration-300 transform hover:scale-105">
                      Learn More
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuthorsHomepage;
